package com.nscorp.ccp.biz.impl.markoff;

import com.google.common.collect.FluentIterable;
import com.nscorp.ccp.biz.markoff.MarkoffStudyCreator;
import com.nscorp.ccp.common.markoff.MarkoffStudy;
import com.nscorp.ccp.common.markoff.MarkoffStudyStatus;
import com.nscorp.ccp.dao.markoff.MarkoffAndStudyDataDAO;
import com.nscorp.ccp.dao.markoff.MarkoffInputDataDAO;
import com.nscorp.ccp.logic.markoff.MarkoffProcessorLogic;
import io.vavr.control.Try;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;

@Service
@Slf4j
@RequiredArgsConstructor
public class MarkoffStudyCreatorImpl implements MarkoffStudyCreator {
    private final MarkoffInputDataDAO markoffInputDataDAO;
    private final MarkoffAndStudyDataDAO markoffAndStudyDataDAO;
    private final MarkoffProcessorLogic markoffProcessorLogic = new MarkoffProcessorLogic();

    @Transactional
    public MarkoffStudy processMarkoffStudy(final @NonNull Integer studyLengthMonths, final @NonNull LocalDate todayDate, final @NonNull String racfId) {
        val markoffStudy = createInitialStudy(studyLengthMonths, todayDate, racfId);
        return Try.of(()->runStudy(markoffStudy, studyLengthMonths)).
                getOrElseGet(exc->writeFailedStudy(exc, markoffStudy));
    }

    private MarkoffStudy createInitialStudy(final @NotNull Integer studyLengthMonths, final @NotNull LocalDate todayDate, final @NonNull String racfId) {
        val firstDayOfTodaysMonth = todayDate.withDayOfMonth(1);

	    val study = MarkoffStudy.builder()
	        .runTs(Instant.now())
	        .startDate(firstDayOfTodaysMonth.minusMonths(studyLengthMonths))
	        .endDate(firstDayOfTodaysMonth.minusDays(1))
	        .status(MarkoffStudyStatus.RUNNING)
	        .creationUser(racfId)
	        .build();
        return markoffAndStudyDataDAO.save(study);
    }

    private MarkoffStudy writeFailedStudy(final Throwable e, final MarkoffStudy study) {
	    val failedStudy = study.toBuilder()
                .status(MarkoffStudyStatus.FAILED)
                .errorMessage(e.toString())
                .build();
	    return markoffAndStudyDataDAO.save(failedStudy);
    }

    private MarkoffStudy runStudy(final MarkoffStudy study, final int studyLengthMonths) {
        val endDateFuture = study.getEndDate().plusDays(1);
        val startDatePast = endDateFuture.minusMonths(studyLengthMonths + 1);
	    val inputData = markoffInputDataDAO.getMarkoffInputDataByDates(startDatePast, endDateFuture, studyLengthMonths);

        log.warn(String.format(
                "Get Markoff Input Data. Memory = %d", Runtime.getRuntime()
                        .totalMemory()));

        val poolData = markoffInputDataDAO.getMarkoffPoolData();

        log.warn(String.format(
                "Get Markoff Pool Data. Memory = %d", Runtime.getRuntime()
                        .totalMemory()));

        val resultData = markoffProcessorLogic.process(inputData, poolData, endDateFuture, studyLengthMonths);

        val markoffDataWithStudyID = FluentIterable.from(resultData)
                .transform(rD -> rD.withMarkoffStudyId(study.getMarkoffStudyId()));

        val savedMarkoffData = markoffAndStudyDataDAO.saveAll(markoffDataWithStudyID);
        val markoffStudyUpdateSuccessful = study.toBuilder()
                .status(MarkoffStudyStatus.COMPLETED)
                .build();
	    return markoffAndStudyDataDAO.save(markoffStudyUpdateSuccessful);
    }
}
