package com.nscorp.ccp.rest;

import com.nscorp.ccp.common.markoff.MarkoffStudyStatus;
import com.nscorp.ccp.dao.markoff.MarkoffAndStudyDataDAO;
import com.nscorp.ccp.rest.json.JsonMarkoffData;
import com.nscorp.ccp.rest.json.JsonMarkoffStudy;
import com.nscorp.ccp.rest.json.JsonCollectionResponse;
import com.nscorp.ccp.rest.mappers.JsonMapper;
import com.nscorp.ccp.common.markoff.MarkoffStudy;
import com.nscorp.ccp.common.markoff.MarkoffData;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.junit.jupiter.api.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@Tag("slow")
@Slf4j
@DisplayName("RestService Top-Level Integration Tests")
public class TestRestService {
    @Mock
    private MarkoffAndStudyDataDAO dao;
    @Mock
    private JsonMapper jsonMapper;
    @Mock
    private HttpServletRequest request;

    @InjectMocks
    private RestService restService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        restService = RestService.builder()
                .dao(dao)
                .jsonMapper(jsonMapper)
                .build();
    }

    @Test
    @DisplayName("Should get all markoff data for latest study successfully")
    void testGetAllMarkoffDataForLatestStudy_found() {
        // Given
        val study = createMockStudy(123L, "Latest Study");
        val data1 = createMockMarkoffData(1L, "D1", "S1");
        val data2 = createMockMarkoffData(2L, "D2", "S2");
        val jsonData = createMockJsonMarkoffData(1L);

        when(dao.getLatestSuccessfulStudy()).thenReturn(study);
        when(dao.getMarkoffDataByStudyId(123L)).thenReturn(Arrays.asList(data1, data2));
        when(jsonMapper.toJson(any(MarkoffData.class))).thenReturn(jsonData);

        // When
        val response = restService.getAllMarkoffDataForLatestStudy(request);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(200, response.getStatusCodeValue());
        verify(dao, times(1)).getLatestSuccessfulStudy();
        verify(dao, times(1)).getMarkoffDataByStudyId(123L);
        verify(jsonMapper, times(2)).toJson(any(MarkoffData.class));
        log.info("Successfully tested getAllMarkoffDataForLatestStudy with found data");
    }

    @Test
    @DisplayName("Should return 404 when no latest study found")
    void testGetAllMarkoffDataForLatestStudy_notFound() {
        // Given
        when(dao.getLatestSuccessfulStudy()).thenReturn(null);

        // When
        val response = restService.getAllMarkoffDataForLatestStudy(request);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertEquals(404, response.getStatusCodeValue());
        verify(dao, times(1)).getLatestSuccessfulStudy();
        verify(dao, never()).getMarkoffDataByStudyId(anyLong());
        log.info("Successfully tested getAllMarkoffDataForLatestStudy with no study found");
    }

    @Test
    @DisplayName("Should get all markoff data by study ID successfully")
    void testGetAllMarkoffData_found() {
        // Given
        val studyId = 1L;
        val study = createMockStudy(studyId, "Test Study");
        val data = createMockMarkoffData(1L, "D1", "S1");
        val jsonData = createMockJsonMarkoffData(1L);

        when(dao.getStudyById(studyId)).thenReturn(study);
        when(dao.getMarkoffDataByStudyId(studyId)).thenReturn(Arrays.asList(data));
        when(jsonMapper.toJson(any(MarkoffData.class))).thenReturn(jsonData);

        // When
        val response = restService.getAllMarkoffData(request, studyId);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(200, response.getStatusCodeValue());
        verify(dao, times(1)).getStudyById(studyId);
        verify(dao, times(1)).getMarkoffDataByStudyId(studyId);
        verify(jsonMapper, times(1)).toJson(any(MarkoffData.class));
        log.info("Successfully tested getAllMarkoffData by study ID: {}", studyId);
    }

    @Test
    @DisplayName("Should return 404 when study ID not found")
    void testGetAllMarkoffData_notFound() {
        // Given
        val studyId = 1L;
        when(dao.getStudyById(studyId)).thenReturn(null);

        // When
        val response = restService.getAllMarkoffData(request, studyId);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertEquals(404, response.getStatusCodeValue());
        verify(dao, times(1)).getStudyById(studyId);
        verify(dao, never()).getMarkoffDataByStudyId(anyLong());
        log.info("Successfully tested getAllMarkoffData with non-existent study ID: {}", studyId);
    }

    @Test
    @DisplayName("Should get all studies successfully")
    void testGetAllStudies() {
        // Given
        val study1 = createMockStudy(1L, "Study 1");
        val study2 = createMockStudy(2L, "Study 2");
        val jsonStudy = createMockJsonStudy(1L, "Study 1");

        when(dao.getAllStudies()).thenReturn(Arrays.asList(study1, study2));
        when(jsonMapper.toJson(any(MarkoffStudy.class))).thenReturn(jsonStudy);

        // When
        val response = restService.getAllStudies(request);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(200, response.getStatusCodeValue());
        verify(dao, times(1)).getAllStudies();
        verify(jsonMapper, times(2)).toJson(any(MarkoffStudy.class));
        log.info("Successfully tested getAllStudies");
    }

    @Test
    @DisplayName("Should get study by ID successfully")
    void testGetStudyById_found() {
        // Given
        val studyId = 1L;
        val study = createMockStudy(studyId, "Individual Study");
        val jsonStudy = createMockJsonStudy(studyId, "Individual Study");

        when(dao.getStudyById(studyId)).thenReturn(study);
        when(jsonMapper.toJson(study)).thenReturn(jsonStudy);

        // When
        val response = restService.getStudyById(studyId);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(200, response.getStatusCodeValue());
        assertEquals(jsonStudy, response.getBody());
        verify(dao, times(1)).getStudyById(studyId);
        verify(jsonMapper, times(1)).toJson(study);
        log.info("Successfully tested getStudyById for ID: {}", studyId);
    }

    @Test
    @DisplayName("Should return 404 when getting study by non-existent ID")
    void testGetStudyById_notFound() {
        // Given
        val studyId = 1L;
        when(dao.getStudyById(studyId)).thenReturn(null);

        // When
        val response = restService.getStudyById(studyId);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertEquals(404, response.getStatusCodeValue());
        assertNull(response.getBody());
        verify(dao, times(1)).getStudyById(studyId);
        verify(jsonMapper, never()).toJson(any(MarkoffStudy.class));
        log.info("Successfully tested getStudyById with non-existent ID: {}", studyId);
    }

    @Test
    @DisplayName("Should handle empty data collections")
    void testHandleEmptyDataCollections() {
        // Given
        val study = createMockStudy(1L, "Empty Study");
        when(dao.getLatestSuccessfulStudy()).thenReturn(study);
        when(dao.getMarkoffDataByStudyId(1L)).thenReturn(Collections.emptyList());

        // When
        val response = restService.getAllMarkoffDataForLatestStudy(request);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        verify(dao, times(1)).getLatestSuccessfulStudy();
        verify(dao, times(1)).getMarkoffDataByStudyId(1L);
        log.info("Successfully tested handling of empty data collections");
    }

    @Test
    @DisplayName("Should handle large study IDs")
    void testHandleLargeStudyIds() {
        // Given
        val largeStudyId = 999999999L;
        val study = createMockStudy(largeStudyId, "Large ID Study");
        val jsonStudy = createMockJsonStudy(largeStudyId, "Large ID Study");

        when(dao.getStudyById(largeStudyId)).thenReturn(study);
        when(jsonMapper.toJson(study)).thenReturn(jsonStudy);

        // When
        val response = restService.getStudyById(largeStudyId);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(jsonStudy, response.getBody());
        verify(dao, times(1)).getStudyById(largeStudyId);
        log.info("Successfully tested handling of large study ID: {}", largeStudyId);
    }

    @Nested
    @DisplayName("Top-Level REST API Integration Tests")
    class TopLevelRestIntegrationTests {

        @Test
        @DisplayName("Should test complete REST workflow for latest markoff data")
        void shouldTestCompleteRestWorkflowForLatestMarkoffData() {
            // Given
            val study = createMockStudy(1L, "Latest Study");
            val markoffDataList = Arrays.asList(
                createMockMarkoffData(1L, "D1", "S1"),
                createMockMarkoffData(2L, "D2", "S2"),
                createMockMarkoffData(3L, "D3", "S3")
            );
            val jsonData = createMockJsonMarkoffData(1L);

            when(dao.getLatestSuccessfulStudy()).thenReturn(study);
            when(dao.getMarkoffDataByStudyId(1L)).thenReturn(markoffDataList);
            when(jsonMapper.toJson(any(MarkoffData.class))).thenReturn(jsonData);

            // When
            val response = restService.getAllMarkoffDataForLatestStudy(request);

            // Then
            assertNotNull(response);
            assertEquals(HttpStatus.OK, response.getStatusCode());

            // Verify complete workflow
            verify(dao, times(1)).getLatestSuccessfulStudy();
            verify(dao, times(1)).getMarkoffDataByStudyId(1L);
            verify(jsonMapper, times(3)).toJson(any(MarkoffData.class));

            log.info("Successfully tested complete REST workflow for latest markoff data with {} items",
                markoffDataList.size());
        }

        @Test
        @DisplayName("Should test complete REST workflow for specific study")
        void shouldTestCompleteRestWorkflowForSpecificStudy() {
            // Given
            val studyId = 123L;
            val study = createMockStudy(studyId, "Specific Study");
            val markoffDataList = Arrays.asList(
                createMockMarkoffData(10L, "DA", "SA"),
                createMockMarkoffData(20L, "DB", "SB")
            );
            val jsonData = createMockJsonMarkoffData(10L);

            when(dao.getStudyById(studyId)).thenReturn(study);
            when(dao.getMarkoffDataByStudyId(studyId)).thenReturn(markoffDataList);
            when(jsonMapper.toJson(any(MarkoffData.class))).thenReturn(jsonData);

            // When
            val response = restService.getAllMarkoffData(request, studyId);

            // Then
            assertNotNull(response);
            assertEquals(HttpStatus.OK, response.getStatusCode());

            // Verify complete workflow
            verify(dao, times(1)).getStudyById(studyId);
            verify(dao, times(1)).getMarkoffDataByStudyId(studyId);
            verify(jsonMapper, times(2)).toJson(any(MarkoffData.class));

            log.info("Successfully tested complete REST workflow for study ID: {} with {} items",
                studyId, markoffDataList.size());
        }

        @Test
        @DisplayName("Should test complete studies management workflow")
        void shouldTestCompleteStudiesManagementWorkflow() {
            // Given
            val studies = Arrays.asList(
                createMockStudy(1L, "Study Alpha"),
                createMockStudy(2L, "Study Beta"),
                createMockStudy(3L, "Study Gamma")
            );
            val jsonStudy = createMockJsonStudy(1L, "Study Alpha");

            when(dao.getAllStudies()).thenReturn(studies);
            when(jsonMapper.toJson(any(MarkoffStudy.class))).thenReturn(jsonStudy);

            // When - Test get all studies
            val allStudiesResponse = restService.getAllStudies(request);

            // Then
            assertNotNull(allStudiesResponse);
            assertEquals(HttpStatus.OK, allStudiesResponse.getStatusCode());
            verify(dao, times(1)).getAllStudies();
            verify(jsonMapper, times(3)).toJson(any(MarkoffStudy.class));

            // When - Test get individual study
            when(dao.getStudyById(1L)).thenReturn(studies.get(0));
            when(jsonMapper.toJson(studies.get(0))).thenReturn(jsonStudy);

            val individualStudyResponse = restService.getStudyById(1L);

            // Then
            assertNotNull(individualStudyResponse);
            assertEquals(HttpStatus.OK, individualStudyResponse.getStatusCode());
            assertEquals(jsonStudy, individualStudyResponse.getBody());

            log.info("Successfully tested complete studies management workflow with {} studies",
                studies.size());
        }

        @Test
        @DisplayName("Should test error handling across all endpoints")
        void shouldTestErrorHandlingAcrossAllEndpoints() {
            // Given - Setup for various error scenarios
            when(dao.getLatestSuccessfulStudy()).thenReturn(null);
            when(dao.getStudyById(anyLong())).thenReturn(null);

            // When & Then - Test latest markoff data endpoint error handling
            val latestResponse = restService.getAllMarkoffDataForLatestStudy(request);
            assertEquals(HttpStatus.NOT_FOUND, latestResponse.getStatusCode());

            // When & Then - Test specific study endpoint error handling
            val specificResponse = restService.getAllMarkoffData(request, 999L);
            assertEquals(HttpStatus.NOT_FOUND, specificResponse.getStatusCode());

            // When & Then - Test individual study endpoint error handling
            val individualResponse = restService.getStudyById(999L);
            assertEquals(HttpStatus.NOT_FOUND, individualResponse.getStatusCode());

            log.info("Successfully tested error handling across all REST endpoints");
        }

        @Test
        @DisplayName("Should test REST service with various data volumes")
        void shouldTestRestServiceWithVariousDataVolumes() {
            // Given - Test with different data volumes
            val dataVolumes = new int[]{0, 1, 10, 100, 1000};

            for (val volume : dataVolumes) {
                // Setup mock data
                val study = createMockStudy(1L, "Volume Test Study");
                val markoffDataList = createMockMarkoffDataList(volume);
                val jsonData = createMockJsonMarkoffData(1L);

                when(dao.getLatestSuccessfulStudy()).thenReturn(study);
                when(dao.getMarkoffDataByStudyId(1L)).thenReturn(markoffDataList);
                when(jsonMapper.toJson(any(MarkoffData.class))).thenReturn(jsonData);

                // When
                val response = restService.getAllMarkoffDataForLatestStudy(request);

                // Then
                assertNotNull(response);
                assertEquals(HttpStatus.OK, response.getStatusCode());

                log.info("Successfully tested REST service with data volume: {} items", volume);

                // Reset mocks for next iteration
                reset(dao, jsonMapper);
            }
        }

        @Test
        @DisplayName("Should test complete JSON mapping integration")
        void shouldTestCompleteJsonMappingIntegration() {
            // Given
            val study = createMockStudy(1L, "JSON Mapping Test");
            val markoffData = createMockMarkoffData(1L, "D1", "S1");
            val jsonStudy = createMockJsonStudy(1L, "JSON Mapping Test");
            val jsonData = createMockJsonMarkoffData(1L);

            when(dao.getStudyById(1L)).thenReturn(study);
            when(dao.getMarkoffDataByStudyId(1L)).thenReturn(Arrays.asList(markoffData));
            when(jsonMapper.toJson(study)).thenReturn(jsonStudy);
            when(jsonMapper.toJson(markoffData)).thenReturn(jsonData);

            // When - Test study JSON mapping
            val studyResponse = restService.getStudyById(1L);

            // Then
            assertNotNull(studyResponse);
            assertEquals(HttpStatus.OK, studyResponse.getStatusCode());
            assertEquals(jsonStudy, studyResponse.getBody());

            // When - Test markoff data JSON mapping
            val dataResponse = restService.getAllMarkoffData(request, 1L);

            // Then
            assertNotNull(dataResponse);
            assertEquals(HttpStatus.OK, dataResponse.getStatusCode());

            // Verify JSON mapping was called
            verify(jsonMapper, times(1)).toJson(study);
            verify(jsonMapper, times(1)).toJson(markoffData);

            log.info("Successfully tested complete JSON mapping integration");
        }
    }

    // Helper methods
    private MarkoffStudy createMockStudy(Long id, String description) {
        return MarkoffStudy.builder()
            .markoffStudyId(id)
            .runTs(Instant.now())
            .startDate(LocalDate.of(2023, 1, 1))
            .endDate(LocalDate.of(2023, 6, 30))
            .status(MarkoffStudyStatus.COMPLETED)
            .description(description)
            .creationUser("test_user")
            .errorMessage("")
            .build();
    }

    private MarkoffData createMockMarkoffData(Long id, String distr, String subDistr) {
        return MarkoffData.builder()
            .markoffDataId(id)
            .markoffStudyId(1L)
            .distr(distr)
            .subDistr(subDistr)
            .poolName("TestPool")
            .craft("TestCraft")
            .distinctCntEmp(100L)
            .empTotalStateDaysMedian(500.0)
            .totalStateDays(1000.0)
            .stateDays(750.0)
            .rate(0.75)
            .empTotalStateDaysAvg(600.0)
            .build();
    }

    private JsonMarkoffData createMockJsonMarkoffData(Long id) {
        val jsonData = mock(JsonMarkoffData.class);
        when(jsonData.getMarkoffDataId()).thenReturn(id);
        return jsonData;
    }

    private JsonMarkoffStudy createMockJsonStudy(Long id, String description) {
        val jsonStudy = mock(JsonMarkoffStudy.class);
        when(jsonStudy.getMarkoffStudyId()).thenReturn(id);
        when(jsonStudy.getDescription()).thenReturn(description);
        return jsonStudy;
    }

    private List<MarkoffData> createMockMarkoffDataList(int size) {
        val dataList = new ArrayList<MarkoffData>();
        for (int i = 1; i <= size; i++) {
            val data = createMockMarkoffData((long) i, "D" + i, "S" + i);
            dataList.add(data);
        }
        return dataList;
    }
}
