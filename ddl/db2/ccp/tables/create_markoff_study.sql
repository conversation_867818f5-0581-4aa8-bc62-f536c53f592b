drop table if exists ccp.markoff_study;
create table ccp.markoff_study(
    markoff_study_id bigint not null generated by default as identity primary key,
    run_ts timestamp not null,
    start_date date not null,
    end_date date not null,
    status varchar(16), -- COMPLETED, FAILED
    description varchar(1024),
    creation_user varchar(16), -- "sys" if created by cron job, else RACF of user
    error_message varchar(1024)
) in scpm_rpt_usr index in scpm_rpt_idx; 

