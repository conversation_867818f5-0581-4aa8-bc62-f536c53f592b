package com.nscorp.ccp.biz.impl.seedData;

import com.nscorp.ccp.biz.seedData.SeedDataLoader;
import com.nscorp.ccp.utils.date.DateTimeProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Instant;

import static com.nscorp.ccp.utils.logging.LogUtils.warn;

@Service
@Slf4j
@RequiredArgsConstructor
public class SeedDataLoaderImpl implements SeedDataLoader {
	private final DateTimeProvider dateTimeProvider;

	@Value("${skipTestInitialization}")
	private boolean skip;

	@Override
	public void load(Instant cutoffTs) {
		load(false, cutoffTs);
	}

	@Override public void load(boolean force, Instant cutoffTs) {
		if (skip) {
			warn(log, "Skipping seed data loading.");
		}
		if (force || ! skip) {
		}
	}
}
