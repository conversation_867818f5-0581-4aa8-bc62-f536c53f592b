package com.nscorp.ccp.biz.impl.markoff;

import com.nscorp.ccp.biz.markoff.MarkoffProcessor;
import com.nscorp.ccp.dao.markoff.MarkoffInputDataDAO;
import com.nscorp.ccp.dao.markoff.MarkoffAndStudyDataDAO;
import com.nscorp.ccp.logic.markoff.MarkoffProcessorLogic;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Service
@RequiredArgsConstructor
@Slf4j
public class MarkoffProcessorImpl implements MarkoffProcessor {
    private final MarkoffInputDataDAO markoffInputDataDAO;
    private final MarkoffAndStudyDataDAO markoffAndStudyDataDAO;
    private final MarkoffProcessorLogic markoffProcessorLogic = new MarkoffProcessorLogic();

    @Override public void processMarkoffData(LocalDate startDate, Integer studyLengthMonths, Long markoffDataId, Long markoffStudyId) {
        val inputData = markoffInputDataDAO.getMarkoffInputData(startDate, studyLengthMonths);
        val poolData = markoffInputDataDAO.getMarkoffPoolData();
        val resultData = markoffProcessorLogic.process(inputData, poolData, startDate, studyLengthMonths);
       // System.out.println(resultData);
        val savedResultData = markoffAndStudyDataDAO.saveAll(resultData);
    }
}
